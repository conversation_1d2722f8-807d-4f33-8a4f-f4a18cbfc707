import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import AuthScreen from '../screens/AuthScreen';
import TabNavigator from '../screens/TabNavigator';
import { useAuth } from '../AuthContext';
import { BACKEND_URL } from '../constants/env';
import AsyncStorage from '@react-native-async-storage/async-storage';


const Stack = createNativeStackNavigator();

export default function Navigation() {
  const { user, loading } = useAuth();
  const [loadingAuthAccess, setLoadingAuthAccess] = useState(false);
  const [hasWorkerAccess, setHasWorkerAccess] = useState<boolean | null>(null);
  const [checkingAccess, setCheckingAccess] = useState(true);

  useEffect(() => {
    setLoadingAuthAccess(true);
    const checkWorkerAccess = async () => {
      if (!user) {
        setHasWorkerAccess(null);
        setCheckingAccess(false);
        return;
      }

      try {
        const token = await AsyncStorage.getItem('supabase_jwt_token');
        if (!token) {
          setHasWorkerAccess(false);
          setCheckingAccess(false);
          return;
        }

        // Vérification auprès du backend pour l'accès worker
        const response = await fetch(`${BACKEND_URL}/verify_access`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          setHasWorkerAccess(true);
        } else {
          setHasWorkerAccess(false);
        }
      } catch (error) {
        console.error('Erreur lors de la vérification de l\'accès worker:', error);
        setHasWorkerAccess(false);
      } finally {
        setCheckingAccess(false);
      }
      setLoadingAuthAccess(false);
    };

    checkWorkerAccess();
  }, [user]);

  if (loading || checkingAccess || loadingAuthAccess) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Chargement...</Text>
      </View>
    );
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!user ? (
          <Stack.Screen name="Auth" component={AuthScreen} />
        ) : hasWorkerAccess ? (
          <Stack.Screen name="Home" component={TabNavigator} />
        ) : (
          <Stack.Screen
            name="UnauthorizedWorker"
            component={() => (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>
                  Accès non autorisé pour les travailleurs
                </Text>
              </View>
            )}
          />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
});
