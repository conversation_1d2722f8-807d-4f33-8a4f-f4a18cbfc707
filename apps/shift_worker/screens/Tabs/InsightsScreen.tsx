import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { BACKEND_URL } from '../../constants/env';

interface WorkerStats {
  total_shifts_worked: number;
  total_hours_worked: number;
  total_earnings: number;
  currency: string;
  average_rating: number;
  shifts_this_month: number;
  hours_this_month: number;
  earnings_this_month: number;
  pending_applications: number;
  accepted_applications: number;
  completed_shifts: number;
}

interface RecentShift {
  id: number;
  title: string;
  venue_name: string;
  date: string;
  hours_worked: number;
  earnings: number;
  status: string;
}

export default function InsightsScreen() {
  const [stats, setStats] = useState<WorkerStats | null>(null);
  const [recentShifts, setRecentShifts] = useState<RecentShift[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchWorkerInsights();
  }, []);

  const fetchWorkerInsights = async () => {
    try {
      setLoading(true);
      const token = await AsyncStorage.getItem('supabase_jwt_token');
      
      if (!token) {
        console.warn("Token non trouvé");
        return;
      }

      // Fetch worker statistics
      const statsResponse = await fetch(`${BACKEND_URL}/get_worker_stats`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      // Fetch recent shifts
      const shiftsResponse = await fetch(`${BACKEND_URL}/get_recent_shifts`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (shiftsResponse.ok) {
        const shiftsData = await shiftsResponse.json();
        setRecentShifts(shiftsData.shifts || []);
      }

    } catch (error) {
      console.error('Erreur lors du chargement des insights:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency: string = 'EUR') => {
    return `${amount.toFixed(2)} ${currency}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#28A745';
      case 'confirmed': return '#007AFF';
      case 'pending': return '#FFC107';
      case 'cancelled': return '#DC3545';
      default: return '#666';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Terminé';
      case 'confirmed': return 'Confirmé';
      case 'pending': return 'En attente';
      case 'cancelled': return 'Annulé';
      default: return status;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Chargement des statistiques...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Mes statistiques</Text>
          <TouchableOpacity onPress={fetchWorkerInsights} style={styles.refreshButton}>
            <Icon name="refresh" size={24} color="#007AFF" />
          </TouchableOpacity>
        </View>

        {stats && (
          <>
            {/* Overview Cards */}
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Icon name="work" size={32} color="#007AFF" />
                <Text style={styles.statNumber}>{stats.total_shifts_worked}</Text>
                <Text style={styles.statLabel}>Shifts travaillés</Text>
              </View>

              <View style={styles.statCard}>
                <Icon name="schedule" size={32} color="#28A745" />
                <Text style={styles.statNumber}>{stats.total_hours_worked}h</Text>
                <Text style={styles.statLabel}>Heures totales</Text>
              </View>

              <View style={styles.statCard}>
                <Icon name="euro-symbol" size={32} color="#FFC107" />
                <Text style={styles.statNumber}>
                  {formatCurrency(stats.total_earnings, stats.currency)}
                </Text>
                <Text style={styles.statLabel}>Gains totaux</Text>
              </View>

              <View style={styles.statCard}>
                <Icon name="star" size={32} color="#FF9500" />
                <Text style={styles.statNumber}>
                  {stats.average_rating ? stats.average_rating.toFixed(1) : 'N/A'}
                </Text>
                <Text style={styles.statLabel}>Note moyenne</Text>
              </View>
            </View>

            {/* This Month Section */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Ce mois-ci</Text>
              <View style={styles.monthlyStats}>
                <View style={styles.monthlyStatItem}>
                  <Text style={styles.monthlyStatNumber}>{stats.shifts_this_month}</Text>
                  <Text style={styles.monthlyStatLabel}>Shifts</Text>
                </View>
                <View style={styles.monthlyStatItem}>
                  <Text style={styles.monthlyStatNumber}>{stats.hours_this_month}h</Text>
                  <Text style={styles.monthlyStatLabel}>Heures</Text>
                </View>
                <View style={styles.monthlyStatItem}>
                  <Text style={styles.monthlyStatNumber}>
                    {formatCurrency(stats.earnings_this_month, stats.currency)}
                  </Text>
                  <Text style={styles.monthlyStatLabel}>Gains</Text>
                </View>
              </View>
            </View>

            {/* Applications Status */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Candidatures</Text>
              <View style={styles.applicationStats}>
                <View style={styles.applicationItem}>
                  <View style={[styles.applicationDot, { backgroundColor: '#FFC107' }]} />
                  <Text style={styles.applicationText}>
                    {stats.pending_applications} en attente
                  </Text>
                </View>
                <View style={styles.applicationItem}>
                  <View style={[styles.applicationDot, { backgroundColor: '#28A745' }]} />
                  <Text style={styles.applicationText}>
                    {stats.accepted_applications} acceptées
                  </Text>
                </View>
                <View style={styles.applicationItem}>
                  <View style={[styles.applicationDot, { backgroundColor: '#007AFF' }]} />
                  <Text style={styles.applicationText}>
                    {stats.completed_shifts} terminées
                  </Text>
                </View>
              </View>
            </View>
          </>
        )}

        {/* Recent Shifts */}
        {recentShifts.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Shifts récents</Text>
            {recentShifts.map((shift) => (
              <View key={shift.id} style={styles.shiftItem}>
                <View style={styles.shiftInfo}>
                  <Text style={styles.shiftTitle}>{shift.title}</Text>
                  <Text style={styles.shiftVenue}>{shift.venue_name}</Text>
                  <Text style={styles.shiftDate}>
                    {new Date(shift.date).toLocaleDateString('fr-FR')}
                  </Text>
                </View>
                <View style={styles.shiftStats}>
                  <Text style={styles.shiftHours}>{shift.hours_worked}h</Text>
                  <Text style={styles.shiftEarnings}>
                    {formatCurrency(shift.earnings, stats?.currency || 'EUR')}
                  </Text>
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor(shift.status) }]}>
                    <Text style={styles.statusText}>{getStatusText(shift.status)}</Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        )}

        {!stats && !loading && (
          <View style={styles.emptyContainer}>
            <Icon name="analytics" size={64} color="#ccc" />
            <Text style={styles.emptyText}>
              Aucune donnée disponible pour le moment
            </Text>
            <Text style={styles.emptySubtext}>
              Commencez à travailler des shifts pour voir vos statistiques
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  scrollContainer: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    width: '48%',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 4,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  monthlyStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  monthlyStatItem: {
    alignItems: 'center',
  },
  monthlyStatNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  monthlyStatLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  applicationStats: {
    gap: 12,
  },
  applicationItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  applicationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  applicationText: {
    fontSize: 16,
    color: '#333',
  },
  shiftItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  shiftInfo: {
    flex: 1,
  },
  shiftTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  shiftVenue: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  shiftDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  shiftStats: {
    alignItems: 'flex-end',
  },
  shiftHours: {
    fontSize: 14,
    color: '#666',
  },
  shiftEarnings: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 4,
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
  },
});
