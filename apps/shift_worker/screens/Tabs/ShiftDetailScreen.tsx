import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, ActivityIndicator, TouchableOpacity, Alert } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { BACKEND_URL } from '../../constants/env';

interface Shift {
  id: number;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  hourly_rate: string;
  currency: string;
  venue_name?: string;
  venue_address?: string;
  requirements?: string;
  status?: string;
}

type ShiftDetailRouteProp = RouteProp<{ ShiftDetail: { shift: Shift } }, 'ShiftDetail'>;

export default function ShiftDetailScreen() {
  const route = useRoute<ShiftDetailRouteProp>();
  const { shift } = route.params;
  const [loading, setLoading] = useState(false);
  const [hasApplied, setHasApplied] = useState(false);

  useEffect(() => {
    checkApplicationStatus();
  }, []);

  const checkApplicationStatus = async () => {
    try {
      const token = await AsyncStorage.getItem('supabase_jwt_token');
      if (!token) return;

      const response = await fetch(`${BACKEND_URL}/check_application_status?shift_id=${shift.id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        setHasApplied(data.has_applied || false);
      }
    } catch (error) {
      console.error('Erreur lors de la vérification du statut de candidature:', error);
    }
  };

  const applyForShift = async () => {
    try {
      setLoading(true);
      const token = await AsyncStorage.getItem('supabase_jwt_token');
      if (!token) return;

      const response = await fetch(`${BACKEND_URL}/apply_for_shift`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ shift_id: shift.id }),
      });

      if (response.ok) {
        setHasApplied(true);
        Alert.alert('Succès', 'Votre candidature a été envoyée avec succès!');
      } else {
        const errorData = await response.json();
        Alert.alert('Erreur', errorData.message || 'Erreur lors de l\'envoi de la candidature');
      }
    } catch (error) {
      console.error('Erreur lors de la candidature:', error);
      Alert.alert('Erreur', 'Une erreur est survenue lors de l\'envoi de la candidature');
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString);
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateDuration = () => {
    const start = new Date(shift.start_time);
    const end = new Date(shift.end_time);
    const durationMs = end.getTime() - start.getTime();
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (minutes === 0) {
      return `${hours}h`;
    }
    return `${hours}h${minutes.toString().padStart(2, '0')}`;
  };

  const calculateTotalPay = () => {
    const start = new Date(shift.start_time);
    const end = new Date(shift.end_time);
    const durationHours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
    const totalPay = durationHours * parseFloat(shift.hourly_rate);
    return totalPay.toFixed(2);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>{shift.title}</Text>
          {shift.venue_name && (
            <Text style={styles.venue}>{shift.venue_name}</Text>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Horaires</Text>
          <Text style={styles.dateTime}>Début: {formatDateTime(shift.start_time)}</Text>
          <Text style={styles.dateTime}>Fin: {formatDateTime(shift.end_time)}</Text>
          <Text style={styles.duration}>Durée: {calculateDuration()}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Rémunération</Text>
          <Text style={styles.rate}>{shift.hourly_rate} {shift.currency}/heure</Text>
          <Text style={styles.totalPay}>
            Total estimé: {calculateTotalPay()} {shift.currency}
          </Text>
        </View>

        {shift.venue_address && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Lieu</Text>
            <Text style={styles.address}>{shift.venue_address}</Text>
          </View>
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.description}>{shift.description}</Text>
        </View>

        {shift.requirements && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Exigences</Text>
            <Text style={styles.requirements}>{shift.requirements}</Text>
          </View>
        )}

        <View style={styles.buttonContainer}>
          {hasApplied ? (
            <View style={styles.appliedButton}>
              <Text style={styles.appliedText}>Candidature envoyée</Text>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.applyButton}
              onPress={applyForShift}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.applyButtonText}>Postuler pour ce shift</Text>
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  venue: {
    fontSize: 18,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  dateTime: {
    fontSize: 16,
    color: '#333',
    marginBottom: 4,
  },
  duration: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  rate: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  totalPay: {
    fontSize: 16,
    color: '#333',
  },
  address: {
    fontSize: 16,
    color: '#333',
  },
  description: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  requirements: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  buttonContainer: {
    marginTop: 20,
  },
  applyButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  applyButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  appliedButton: {
    backgroundColor: '#28A745',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  appliedText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
});
