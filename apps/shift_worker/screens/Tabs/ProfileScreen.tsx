import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator, Image, ScrollView, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';

import AsyncStorage from '@react-native-async-storage/async-storage';

import { BACKEND_URL } from '../../constants/env';

interface WorkerProfile {
  id: number;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  birthdate?: string;
  address?: string;
  postal_code?: string;
  town?: string;
  country?: string;
  bio?: string;
  experience_level?: string;
  language_level?: string;
  mobility_radius?: number;
  available_days?: string[];
  available_hours?: string;
  profile_picture?: string;
  created_at?: string;
}

export default function ProfileScreen() {
  const [profile, setProfile] = useState<WorkerProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchWorkerProfile();
  }, []);

  const fetchWorkerProfile = async () => {
    setLoading(true);
    try {
      const token = await AsyncStorage.getItem('supabase_jwt_token');
      if (!token) {
        console.warn("Token non trouvé");
        return;
      }

      const response = await fetch(`${BACKEND_URL}/get_worker_profile`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Profil worker:", data);
        setProfile(data);
      } else {
        console.error("Erreur API", response.status);
        setProfile(null);
      }

    } catch (error) {
      console.error('Erreur fetch profil worker:', error);
      setProfile(null);
    } finally {
      setLoading(false);
    }
  };

  const formatAvailableDays = (days: string[]) => {
    if (!days || days.length === 0) return 'Non spécifié';
    
    const dayNames: { [key: string]: string } = {
      'monday': 'Lundi',
      'tuesday': 'Mardi',
      'wednesday': 'Mercredi',
      'thursday': 'Jeudi',
      'friday': 'Vendredi',
      'saturday': 'Samedi',
      'sunday': 'Dimanche'
    };
    
    return days.map(day => dayNames[day] || day).join(', ');
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.center}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Chargement du profil...</Text>
      </SafeAreaView>
    );
  }

  if (!profile) {
    return (
      <SafeAreaView style={styles.center}>
        <Text style={styles.errorText}>Impossible de charger le profil</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchWorkerProfile}>
          <Text style={styles.retryText}>Réessayer</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          {profile.profile_picture ? (
            <Image source={{ uri: profile.profile_picture }} style={styles.profileImage} />
          ) : (
            <View style={styles.placeholderImage}>
              <Text style={styles.placeholderText}>
                {profile.first_name?.[0]}{profile.last_name?.[0]}
              </Text>
            </View>
          )}
          <Text style={styles.name}>
            {profile.first_name} {profile.last_name}
          </Text>
          {profile.experience_level && (
            <Text style={styles.experience}>
              Niveau d'expérience: {profile.experience_level}
            </Text>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations personnelles</Text>
          {profile.email && (
            <View style={styles.infoRow}>
              <Text style={styles.label}>Email:</Text>
              <Text style={styles.value}>{profile.email}</Text>
            </View>
          )}
          {profile.phone && (
            <View style={styles.infoRow}>
              <Text style={styles.label}>Téléphone:</Text>
              <Text style={styles.value}>{profile.phone}</Text>
            </View>
          )}
          {profile.birthdate && (
            <View style={styles.infoRow}>
              <Text style={styles.label}>Date de naissance:</Text>
              <Text style={styles.value}>
                {new Date(profile.birthdate).toLocaleDateString('fr-FR')}
              </Text>
            </View>
          )}
        </View>

        {(profile.address || profile.town || profile.country) && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Adresse</Text>
            {profile.address && (
              <Text style={styles.value}>{profile.address}</Text>
            )}
            {(profile.postal_code || profile.town) && (
              <Text style={styles.value}>
                {profile.postal_code} {profile.town}
              </Text>
            )}
            {profile.country && (
              <Text style={styles.value}>{profile.country}</Text>
            )}
          </View>
        )}

        {profile.bio && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Bio</Text>
            <Text style={styles.bioText}>{profile.bio}</Text>
          </View>
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Compétences et disponibilités</Text>
          {profile.language_level && (
            <View style={styles.infoRow}>
              <Text style={styles.label}>Niveau de langue:</Text>
              <Text style={styles.value}>{profile.language_level}</Text>
            </View>
          )}
          {profile.mobility_radius && (
            <View style={styles.infoRow}>
              <Text style={styles.label}>Rayon de mobilité:</Text>
              <Text style={styles.value}>{profile.mobility_radius} km</Text>
            </View>
          )}
          {profile.available_days && (
            <View style={styles.infoRow}>
              <Text style={styles.label}>Jours disponibles:</Text>
              <Text style={styles.value}>
                {formatAvailableDays(profile.available_days)}
              </Text>
            </View>
          )}
          {profile.available_hours && (
            <View style={styles.infoRow}>
              <Text style={styles.label}>Heures disponibles:</Text>
              <Text style={styles.value}>{profile.available_hours}</Text>
            </View>
          )}
        </View>

        {profile.created_at && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Informations du compte</Text>
            <View style={styles.infoRow}>
              <Text style={styles.label}>Membre depuis:</Text>
              <Text style={styles.value}>
                {new Date(profile.created_at).toLocaleDateString('fr-FR')}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 15,
  },
  placeholderImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  placeholderText: {
    color: '#fff',
    fontSize: 36,
    fontWeight: 'bold',
  },
  name: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 5,
  },
  experience: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
    width: 140,
  },
  value: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  bioText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
});
