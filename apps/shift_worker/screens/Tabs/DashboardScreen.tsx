import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  StyleSheet
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { BACKEND_URL } from '../../constants/env';
import ShiftDetailScreen from './ShiftDetailScreen';

interface Shift {
  id: number;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  hourly_rate: string;
  currency: string;
  venue_name?: string;
  status?: string;
}

const Stack = createNativeStackNavigator();

export function DashboardStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="DashboardMain"
        component={DashboardScreen}
        options={{ title: 'Tableau de bord' }}
      />
      <Stack.Screen
        name="ShiftDetail"
        component={ShiftDetailScreen}
        options={{ title: 'Détails du shift' }}
      />
    </Stack.Navigator>
  );
}

export default function DashboardScreen() {
  const navigation = useNavigation();
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAvailableShifts();
  }, []);

  const fetchAvailableShifts = async () => {
    try {
      setLoading(true);
      const token = await AsyncStorage.getItem('supabase_jwt_token');
      
      if (!token) {
        console.warn("Token non trouvé");
        return;
      }

      const response = await fetch(`${BACKEND_URL}/get_available_shifts`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        setShifts(data.shifts || []);
      } else {
        console.error('Erreur lors du chargement des shifts');
      }
    } catch (error) {
      console.error('Erreur de requête:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString);
    return date.toLocaleDateString('fr-FR', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderShiftItem = ({ item }: { item: Shift }) => (
    <TouchableOpacity
      style={styles.shiftCard}
      onPress={() => navigation.navigate('ShiftDetail', { shift: item })}
    >
      <View style={styles.shiftHeader}>
        <Text style={styles.shiftTitle}>{item.title}</Text>
        <Text style={styles.shiftRate}>
          {item.hourly_rate} {item.currency}/h
        </Text>
      </View>
      
      {item.venue_name && (
        <Text style={styles.venueName}>{item.venue_name}</Text>
      )}
      
      <Text style={styles.shiftTime}>
        {formatDateTime(item.start_time)} - {formatDateTime(item.end_time)}
      </Text>
      
      <Text style={styles.shiftDescription} numberOfLines={2}>
        {item.description}
      </Text>
      
      {item.status && (
        <View style={[styles.statusBadge, 
          item.status === 'applied' ? styles.appliedStatus : styles.availableStatus
        ]}>
          <Text style={styles.statusText}>
            {item.status === 'applied' ? 'Candidature envoyée' : 'Disponible'}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Chargement des shifts...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Shifts disponibles</Text>
        <TouchableOpacity onPress={fetchAvailableShifts} style={styles.refreshButton}>
          <Text style={styles.refreshText}>Actualiser</Text>
        </TouchableOpacity>
      </View>

      {shifts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Aucun shift disponible pour le moment</Text>
        </View>
      ) : (
        <FlatList
          data={shifts}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderShiftItem}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  refreshText: {
    color: '#007AFF',
    fontSize: 16,
  },
  listContainer: {
    padding: 15,
  },
  shiftCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  shiftHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  shiftTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  shiftRate: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  venueName: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  shiftTime: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  shiftDescription: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 8,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  appliedStatus: {
    backgroundColor: '#FFF3CD',
  },
  availableStatus: {
    backgroundColor: '#D4EDDA',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
