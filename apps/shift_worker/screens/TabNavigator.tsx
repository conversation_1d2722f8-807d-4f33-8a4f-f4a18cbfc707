import React, { useEffect, useState } from 'react';
import {
  createBottomTabNavigator
} from '@react-navigation/bottom-tabs';
import {
    Image,
    Text,
    TouchableOpacity,
    View,
    StyleSheet,
    Modal,
    Pressable,
    SafeAreaView
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

import DashboardScreen, { DashboardStack } from './Tabs/DashboardScreen';
import ProfileScreen from './Tabs/ProfileScreen';
import InsightsScreen from './Tabs/InsightsScreen';
import { useAuth } from '../AuthContext';

import { BACKEND_URL } from '../constants/env';


const Tab = createBottomTabNavigator();

interface WorkerProfile {
  id: number;
  first_name: string;
  last_name: string;
  [key: string]: any;
}

// Composant pour le bouton de profil/déconnexion
function ProfileTabButton({ onPress, ...props }: any) {
  return (
    <TouchableOpacity
      {...props}
      onPress={onPress}
      style={[props.style, styles.profileButton]}
    >
      <Icon name="person" size={24} color={props.focused ? '#007AFF' : 'gray'} />
      <Text style={{ color: props.focused ? '#007AFF' : 'gray', fontSize: 12 }}>
        Profil
      </Text>
    </TouchableOpacity>
  );
}

export default function TabNavigator() {
  const { signOut } = useAuth();
  const [workerProfile, setWorkerProfile] = useState<WorkerProfile | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    const fetchWorkerProfile = async () => {
      try {
        const token = await AsyncStorage.getItem('supabase_jwt_token');
        if (!token) return;

        const response = await fetch(`${BACKEND_URL}/get_worker_profile`, {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (response.ok) {
          const data = await response.json();
          setWorkerProfile(data);
        }
      } catch (error) {
        console.error('Erreur lors du chargement du profil worker:', error);
      }
    };

    fetchWorkerProfile();
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut();
      setModalVisible(false);
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    }
  };

  return (
    <>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          headerShown: false,
          tabBarActiveTintColor: '#007AFF',
          tabBarInactiveTintColor: 'gray',
          tabBarIcon: ({ focused, color, size }) => {
            if (route.name === 'Dashboard') {
              return <Icon name={focused ? 'home-filled' : 'home'} size={size} color={color} />;
            }
            if (route.name === 'Profile') {
              return <Icon name={focused ? 'person' : 'person-outline'} size={size} color={color} />;
            }
            if (route.name === 'Insights') {
              return <Icon name={focused ? 'analytics' : 'analytics'} size={size} color={color} />;
            }
            return null;
          },
        })}
      >
        <Tab.Screen name="Dashboard" component={DashboardStack} />
        <Tab.Screen name="Profile" component={ProfileScreen} />
        <Tab.Screen name="Insights" component={InsightsScreen} />
        <Tab.Screen
          name="Menu"
          component={() => <View />} // écran vide car modal s'ouvre à la place
          options={{
            tabBarButton: (props) => (
              <ProfileTabButton
                {...props}
                onPress={() => {
                  setModalVisible(true);
                }}
              />
            ),
          }}
        />
      </Tab.Navigator>

      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => {
          setModalVisible(false);
        }}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setModalVisible(false)}
        >
          <View style={styles.modalContent}>
            <SafeAreaView>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Menu</Text>
                <TouchableOpacity
                  onPress={() => setModalVisible(false)}
                  style={styles.closeButton}
                >
                  <Icon name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              {workerProfile && (
                <View style={styles.profileInfo}>
                  <Text style={styles.profileName}>
                    {workerProfile.first_name} {workerProfile.last_name}
                  </Text>
                </View>
              )}

              <TouchableOpacity
                style={styles.menuItem}
                onPress={handleSignOut}
              >
                <Icon name="logout" size={24} color="#FF3B30" />
                <Text style={[styles.menuText, { color: '#FF3B30' }]}>
                  Se déconnecter
                </Text>
              </TouchableOpacity>
            </SafeAreaView>
          </View>
        </Pressable>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  profileButton: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    minHeight: 300,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  profileInfo: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  profileName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  menuText: {
    fontSize: 16,
    marginLeft: 15,
    fontWeight: '500',
  },
});
