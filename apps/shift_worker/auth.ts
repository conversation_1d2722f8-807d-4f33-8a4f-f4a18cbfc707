// Point d'entrée principal pour l'authentification
// Centralise tous les exports liés à l'authentification

// Contexte et Provider
export { AuthProvider, useAuth } from './AuthContext';

// Service d'authentification
export { AuthService } from './services/auth';

// Types
export type { User } from './useAuth';

// Hook personnalisé (alias)
export { useAuth as useAuthentication } from './useAuth';
