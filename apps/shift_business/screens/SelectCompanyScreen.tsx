import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BACKEND_URL } from '../constants/env';

interface Company {
  id: number;
  name: string;
}

interface Props {
  onSelectCompany: (company: Company) => void;
}

export default function SelectCompanyScreen({ onSelectCompany }: Props) {
    const [companies, setCompanies] = useState<Company[]>([]);

    useEffect(() => {
        const fetchCompanies = async () => {
        const token = await AsyncStorage.getItem('supabase_jwt_token');
        if (!token) return;

        try {
            const res = await fetch(`${BACKEND_URL}/get_companies`, {
            headers: { Authorization: `Bearer ${token}` },
            });
            const data = await res.json();
            setCompanies(data.companies);
        } catch (error) {
            console.error('Erreur lors de la récupération des entreprises :', error);
        }
        };


        fetchCompanies();
    }, []);

    const handleSelectCompany = async (company: Company) => {
        try {
        // Stocke la company dans AsyncStorage sous la clé "selectedCompany"
            await AsyncStorage.setItem('selectedCompany', JSON.stringify(company));
        // Informe le parent que la company est sélectionnée
            onSelectCompany(company);
        } catch (error) {
            console.error('Erreur stockage company :', error);
        }
    };

    const renderItem = ({ item }: { item: Company }) => (
        <TouchableOpacity style={styles.item} onPress={() => handleSelectCompany(item)}>
            <Text>{item.name}</Text>
        </TouchableOpacity>
    );

    return (
        <View style={styles.container}>
        <Text style={styles.title}>Choisissez une entreprise :</Text>
        <FlatList
            data={companies}
            keyExtractor={(item) => item.id.toString()}
            renderItem={renderItem}
        />
        </View>
    );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 100,
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    marginBottom: 20,
  },
  item: {
    padding: 15,
    backgroundColor: '#eee',
    marginBottom: 10,
    borderRadius: 8,
  },
});
