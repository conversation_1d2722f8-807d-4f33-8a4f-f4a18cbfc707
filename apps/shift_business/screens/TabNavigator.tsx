import React, { useEffect, useState } from 'react';
import {
  createBottomTabNavigator
} from '@react-navigation/bottom-tabs';
import {
    Image,
    Text,
    TouchableOpacity,
    View,
    StyleSheet,
    Modal,
    Pressable,
    SafeAreaView
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

import DashboardScreen, { DashboardStack } from './Tabs/DashboardScreen';
import { TeamStack } from './Tabs/TeamScreen';
import ProfileScreen from './Tabs/ProfileScreen';
import SelectCompanyScreen from './SelectCompanyScreen';

import { BACKEND_URL } from '../constants/env';


const Tab = createBottomTabNavigator();

interface Company {
  id: number;
  name: string;
  [key: string]: any;
}

export default function TabNavigator() {
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [selectedLieu, setSelectedLieu] = useState<any | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [changerLieu, setChangerLieu] = useState(false);
  const [lieux, setLieux] = useState<any[]>([]);

  useEffect(() => {
    const loadSelectedCompany = async () => {
      try {
        const json = await AsyncStorage.getItem('selectedCompany');
        if (json) {
          const company = JSON.parse(json);
          setSelectedCompany(company);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération de la company stockée:', error);
      }
    };
    loadSelectedCompany();
  }, []);

  useEffect(() => {
    if (!modalVisible) {
      setChangerLieu(false); // reset quand la modale est fermée
    }
  }, [modalVisible]);



    

    useEffect(() => {
        if (selectedCompany) {
            const getFirstLieu = async () => {
                const token = await AsyncStorage.getItem('supabase_jwt_token');
    
                const res = await fetch(`${BACKEND_URL}/get_lieux?company_id=${selectedCompany?.id}`, {
                    headers: { Authorization: `Bearer ${token}` },
                });

                if (res.ok) {
                    const data = await res.json();
                    console.log("lieux : ", data.lieux[0]);
                    setSelectedLieu(data.lieux[0]);
                }
            };
            getFirstLieu()
        }
    }, [selectedCompany]);

    useEffect(() => {
        const fetchLieux = async () => {
            const token = await AsyncStorage.getItem('supabase_jwt_token');

            if (changerLieu) {
                const res = await fetch(`${BACKEND_URL}/get_lieux?company_id=${selectedCompany?.id}`, {
                    headers: { Authorization: `Bearer ${token}` },
                });

                if (res.ok) {
                    const data = await res.json();
                    console.log("lieux : ", data);
                    setLieux(data.lieux);
                }

            }
        };
        fetchLieux()
    }, [changerLieu]);

  if (!selectedCompany) {
    // Forcer le choix de company au premier lancement
    return <SelectCompanyScreen
      onSelectCompany={(company) => {
        setSelectedCompany(company);
        AsyncStorage.setItem('selectedCompany', JSON.stringify(company));
      }}
    />;
  }

  const onResetCompany = () => {
    // Ta logique pour réinitialiser ou rechoisir restaurant
    // Exemple simple :
    setModalVisible(false);
    setSelectedCompany(null); // Ou autre action
    setSelectedLieu(null);
    setLieux([]);
  };

  // Bouton custom pour tab company
  function CompanyTabButton({ onPress, accessibilityState }: any) {
    const focused = accessibilityState?.selected ?? false; // sécurise l'accès à selected
    return (
      <TouchableOpacity
        onPress={onPress}
        style={styles.companyTabButton}
        activeOpacity={0.7}
      >
        <Image source={{ uri: selectedLieu?.logo }} style={{ width: 28, height: 28 }} />
        {/* <Icon url={selectedLieu?.logo || ''} name="store" size={28} color={focused ? '#007AFF' : 'gray'} /> */}
        <Text style={{ color: focused ? '#007AFF' : 'gray', fontSize: 12 }}>
            {selectedLieu?.name}
        </Text>
      </TouchableOpacity>
    );
  }
  

  return (
    <>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          headerShown: false,
          tabBarActiveTintColor: '#007AFF',
          tabBarInactiveTintColor: 'gray',
          tabBarIcon: ({ focused, color, size }) => {
            if (route.name === 'Dashboard') {
              return <Icon name={focused ? 'home-filled' : 'home'} size={size} color={color} />;
            }
            if (route.name === 'Profile') {
              return <Icon name={focused ? 'person' : 'person-outline'} size={size} color={color} />;
            }
            if (route.name === 'Equipe') {
                return <Icon name={focused ? 'group' : 'group'} size={size} color={color} />;
              }
            return null;
          },
        })}
      >
        <Tab.Screen name="Dashboard">
            {() => <DashboardStack venueId={selectedLieu?.id} />}
        </Tab.Screen>

        <Tab.Screen name="Profile" >
            {() => <ProfileScreen venueId={selectedLieu?.id} />}
        </Tab.Screen>
        <Tab.Screen name="Equipe" >
            {() => <TeamStack venueId={selectedLieu?.id} />}
        </Tab.Screen>
        <Tab.Screen
          name="Company"
          component={() => <View />} // écran vide car modal s’ouvre à la place
          options={{
            tabBarButton: (props) => (
              <CompanyTabButton
                {...props}
                onPress={() => {
                  setModalVisible(true);
                }}
              />
            ),
          }}
        />
      </Tab.Navigator>

      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => {
            setModalVisible(false);
        }}
      >
        <Pressable style={styles.modalOverlay} onPress={() => setModalVisible(false)}>
          <SafeAreaView style={styles.modalContent}>
              {!changerLieu ? (
                <>
                    <Text style={styles.modalTitle}>Options Company</Text>
                    <TouchableOpacity style={styles.modalButton} onPress={onResetCompany}>
                        <Text style={styles.modalButtonText}>Changer d'entreprise</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.modalButton} onPress={() => setChangerLieu(true)}>
                        <Text style={styles.modalButtonText}>Changer de lieu</Text>
                    </TouchableOpacity>
                </>
              ) : (
                <>
                    <Text style={styles.modalTitle}>Changer de lieu</Text>
                    {lieux.map((lieu) => (
                        <TouchableOpacity key={lieu.id} style={styles.modalButton} onPress={() => {
                                setSelectedLieu(lieu)
                                setModalVisible(false);
                            }}>
                            <Text style={styles.modalButtonText}>{lieu.name}</Text>
                        </TouchableOpacity>
                    ))}
                </>
              )}
          </SafeAreaView>
        </Pressable>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  companyTabButton: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 4,
  },
  modalOverlay: {
    flex: 1,
    // backgroundColor: '#00000066',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    maxHeight: '50%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 20,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalButton: {
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  modalButtonText: {
    fontSize: 16,
    color: '#007AFF',
    textAlign: 'center',
  },
});
