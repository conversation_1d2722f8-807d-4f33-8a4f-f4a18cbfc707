import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator, Image, ScrollView, StyleSheet, SafeAreaView } from 'react-native';

import AsyncStorage from '@react-native-async-storage/async-storage';


import { BACKEND_URL } from '../../constants/env';


interface Venue {
  id: number;
  created_at?: string;
  company?: number;
  type?: string;
  cuisine?: string;
  name?: string;
  email?: string;
  phone_number?: string;
  adresse?: string;
  postal_code?: string;
  town?: string;
  country?: string;
  logo?: string;
}

interface ProfileScreenProps {
  venueId: number | null;
}

export default function ProfileScreen({ venueId }: ProfileScreenProps) {
    const [venue, setVenue] = useState<Venue | null>(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        console.log("lieuId : ", venueId);
        if (venueId === null) {
            setVenue(null);
        return;
        }
    
        const fetchLieuInfo = async () => {
        setLoading(true);
        try {
            const token = await AsyncStorage.getItem('supabase_jwt_token');
            if (!token) {
            console.warn("Token non trouvé");
            return;
            }
    
            const res = await fetch(`${BACKEND_URL}/get_lieux_info?venue_id=${venueId}`, {
            headers: { Authorization: `Bearer ${token}` },
            });
    
            if (res.ok) {
            const data = await res.json();
            console.log("datas : ", data);
            setVenue(data);
            } else {
            console.error("Erreur API", res.status);
            setVenue(null);
            }
    
        } catch (error) {
            console.error('Erreur fetch lieu:', error);
            setVenue(null);
        } finally {
            setLoading(false);
        }
        };
    
        fetchLieuInfo();
    }, [venueId]);
  

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text>Chargement des informations du profil...</Text>
      </View>
    );
  }

  if (!venue) {
    return (
      <View style={styles.center}>
        <Text>Aucune information de venue disponible.</Text>
      </View>
    );
  }

  return (
    <SafeAreaView>
        <ScrollView contentContainerStyle={styles.container}>
        {venue.logo ? (
            <Image source={{ uri: venue.logo }} style={styles.logo} />
        ) : null}
        <Text style={styles.title}>{venue.name}</Text>
        <Text style={styles.info}>{venue.type} - {venue.cuisine}</Text>
        <Text style={styles.info}>{venue.adresse}, {venue.postal_code} {venue.town}</Text>
        <Text style={styles.info}>{venue.country}</Text>
        <Text style={styles.info}>Email: {venue.email}</Text>
        <Text style={styles.info}>Téléphone: {venue.phone_number}</Text>
        <Text style={styles.info}>Entreprise ID: {venue.company}</Text>
        <Text style={styles.info}>Créé le: {venue.created_at ? new Date(venue.created_at).toLocaleDateString() : 'N/A'}</Text>
        </ScrollView>

    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  center: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
  container: { padding: 20, alignItems: 'center' },
  logo: { width: 120, height: 120, borderRadius: 10, marginBottom: 20 },
  title: { fontSize: 26, fontWeight: '700', marginBottom: 10, textAlign: 'center' },
  info: { fontSize: 16, marginBottom: 8, textAlign: 'center' },
});
