import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import WorkerDetailScreen from './WorkerDetailsScreen';

import AsyncStorage from '@react-native-async-storage/async-storage';

import { BACKEND_URL } from '../../constants/env';

const Stack = createNativeStackNavigator();

export function TeamStack({ venueId }: { venueId: number }) {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="Team"
        options={{ title: 'Équipe' }}
      >
        {() => <TeamScreen venueId={venueId} />}
      </Stack.Screen>

      <Stack.Screen
        name="WorkerDetail"
        component={WorkerDetailScreen}
        options={{ title: 'Détails du membre' }}
      />
    </Stack.Navigator>
  );
}


type Worker = {
  id: number;
  first_name: string;
  last_name: string;
  experience_level: string;
  profile_picture?: string;
};

type TeamRouteProp = RouteProp<{ Team: { venueId: number } }, 'Team'>;

export default function TeamScreen({ venueId }: { venueId: number }) {
    const navigation = useNavigation();

    const [workers, setWorkers] = useState<Worker[]>([]);
    const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchWorkers = async () => {
      try {
        setLoading(true);
        const token = await AsyncStorage.getItem('supabase_jwt_token');

        const res = await fetch(`${BACKEND_URL}/get_venue_workers?venue_id=${venueId}`, {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (res.ok) {
          const data = await res.json();
          setWorkers(data);
        } else {
          console.error('Erreur lors du chargement de l’équipe');
        }
      } catch (err) {
        console.error('Erreur de requête :', err);
      } finally {
        setLoading(false);
      }
    };

    fetchWorkers();
  }, [venueId]);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <ActivityIndicator size="large" />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={workers}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={{ padding: 20 }}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.card}
            onPress={() => navigation.navigate('WorkerDetail', { workerId: item.id })}
          >
            {/* <Image
              source={
                item.profile_picture
                  ? { uri: item.profile_picture }
                  : require('../assets/default-avatar.png') // image par défaut
              }
              style={styles.image}
            /> */}
            <View style={{ flex: 1 }}>
              <Text style={styles.name}>
                {item.first_name} {item.last_name}
              </Text>
              <Text style={styles.experience}>{item.experience_level}</Text>
            </View>
          </TouchableOpacity>
        )}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f2f2f7',
  },
  card: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    elevation: 2,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 15,
  },
  name: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  experience: {
    fontSize: 14,
    color: '#777',
    marginTop: 4,
  },
});
