import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Image, SafeAreaView, ActivityIndicator } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import { BACKEND_URL } from '../../constants/env';
import AsyncStorage from '@react-native-async-storage/async-storage';

type Worker = {
  id: number;
  first_name: string;
  last_name: string;
  birthdate?: string;
  phone?: string;
  email?: string;
  address?: string;
  postal_code?: string;
  town?: string;
  country?: string;
  bio?: string;
  experience_level?: string;
  language_level?: string;
  mobility_radius?: number;
  available_days?: string[];
  available_hours?: string;
  profile_picture?: string;
};

type WorkerDetailRouteProp = RouteProp<{ WorkerDetail: { workerId: number } }, 'WorkerDetail'>;

export default function WorkerDetailScreen() {
  const route = useRoute<WorkerDetailRouteProp>();
  const { workerId } = route.params;

  const [worker, setWorker] = useState<Worker | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchWorkerDetails() {
        const token = await AsyncStorage.getItem('supabase_jwt_token');

        try {
            const response = await fetch(`${BACKEND_URL}/get_worker_info?worker_id=${workerId}`, {
                headers: { Authorization: `Bearer ${token}` },
            });
            if (response.ok) {
                const data = await response.json();
                setWorker(data); // ou ce que tu veux faire avec les données
            } else {
                console.error('Erreur lors de la récupération des infos du worker');
            }
        } catch (error) {
            console.error("Erreur lors du chargement du profil du worker :", error);
        } finally {
            setLoading(false);
        }
    }

    fetchWorkerDetails();
  }, [workerId]);

  if (loading) {
    return (
      <SafeAreaView style={styles.centered}>
        <ActivityIndicator size="large" />
      </SafeAreaView>
    );
  }

  if (!worker) {
    return (
      <SafeAreaView style={styles.centered}>
        <Text>Impossible de charger les informations du travailleur.</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
        <Image source={{ uri: worker.profile_picture }} style={styles.image} />
      {/* {worker.profile_picture ? (
      ) : (
        // <Image source={require('../assets/default-avatar.png')} style={styles.image} />
      )} */}

      <Text style={styles.name}>{worker.first_name} {worker.last_name}</Text>
      <Text style={styles.label}>📍 {worker.town}, {worker.country}</Text>
      <Text style={styles.label}>🎓 Expérience : {worker.experience_level}</Text>
      <Text style={styles.label}>🗣️ Langues : {worker.language_level}</Text>
      <Text style={styles.label}>📝 Bio :</Text>
      <Text style={styles.bio}>{worker.bio || 'Non renseignée'}</Text>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: 110,
    height: 110,
    borderRadius: 55,
    marginBottom: 15,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 10,
  },
  label: {
    fontSize: 16,
    marginVertical: 5,
    color: '#444',
  },
  bio: {
    fontSize: 15,
    textAlign: 'center',
    color: '#666',
    marginTop: 8,
  },
});
