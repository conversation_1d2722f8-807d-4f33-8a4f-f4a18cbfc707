import { createClient } from '@supabase/supabase-js';
import 'react-native-url-polyfill/auto';
import { SUPABASE_URL, SUPABASE_ANON_KEY, BACKEND_URL } from '../constants/env';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const ACCESS_TOKEN_KEY = 'supabase_jwt_token';


// Service d'authentification simplifié
export class AuthService {

    static async verify_access(access_token: string) {
        // Vérification auprès du backend
        console.log(`${BACKEND_URL}/verify_access`)
        const response = await fetch(`${BACKEND_URL}/verify_access`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${access_token}`,
                'Content-Type': 'application/json',
            },
        });

        console.log(response)

        if (response.ok) {
            throw new Error('Accès refusé par le serveur');
            return false
        }
        return true
    }
  // Connexion
  static async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    const access_token = data.session?.access_token;
    if (!access_token) throw new Error('Token JWT non disponible');

    // Stocker le token localement
    await AsyncStorage.setItem(ACCESS_TOKEN_KEY, access_token);

    // // Vérification auprès du backend
    // console.log(`${BACKEND_URL}/verify_access`)
    // const response = await fetch(`${BACKEND_URL}/verify_access`, {
    //     method: 'POST',
    //     headers: {
    //         'Authorization': `Bearer ${access_token}`,
    //         'Content-Type': 'application/json',
    //     },
    // });

    // console.log(response)

    // if (!response.ok) {
    //     throw new Error('Accès refusé par le serveur');
    //     return data, false
    // }

    return data;
  }

  // Déconnexion
  static async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }

  // Récupérer l'utilisateur actuel
  static async getCurrentUser() {
    const { data } = await supabase.auth.getUser();
    return data.user;
  }

  // Écouter les changements d'authentification
  static onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }
}

// Export pour compatibilité
export const getCurrentUser = AuthService.getCurrentUser;
