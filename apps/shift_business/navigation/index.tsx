import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import AuthScreen from '../screens/AuthScreen';
import TabNavigator from '../screens/TabNavigator';
import { useAuth } from '../AuthContext';
import UnauthorizedEmployerScreen from '../screens/UnauthorizedEmployerScreen';
import { BACKEND_URL } from '../constants/env';
import AsyncStorage from '@react-native-async-storage/async-storage';


const Stack = createNativeStackNavigator();


// Composant de chargement
function LoadingScreen() {
  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#0000ff" />
      <Text style={styles.loadingText}>Chargement...</Text>
    </View>
  );
}

export default function Navigation() {
    const { user, loading } = useAuth();
    const [checkingEmployer, setCheckingEmployer] = useState(true);
    const [hasEmployerAccess, setHasEmployerAccess] = useState<boolean | null>(null);

    useEffect(() => {
        const checkEmployerAccess = async () => {
        if (!user) return;

        try {
            const token = await AsyncStorage.getItem("supabase_jwt_token");

            const res = await fetch(`${BACKEND_URL}/verify_access`, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
            });
            
            console.log("auth response : ", res, res.ok)
            if (res.ok) {
                const data = await res.json();
                setHasEmployerAccess(true); // booléen attendu
            } else {
                setHasEmployerAccess(false);
            }
        } catch (error) {
            console.error("Erreur de vérification d'accès employeur :", error);
            setHasEmployerAccess(false);
        } finally {
            setCheckingEmployer(false);
        }
        };

        if (user) {
        checkEmployerAccess();
        }
    }, [user]);

  if (loading || (user && checkingEmployer)) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!user ? (
                <Stack.Screen name="Auth" component={AuthScreen} />
            ) : hasEmployerAccess ? (
                <Stack.Screen name="Home" component={TabNavigator} />
            ) : (
                <Stack.Screen
                    name="UnauthorizedEmployer"
                    component={UnauthorizedEmployerScreen}
                />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
});
