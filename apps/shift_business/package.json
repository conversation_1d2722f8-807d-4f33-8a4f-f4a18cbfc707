{"name": "shift_business", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.8", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.24", "@supabase/supabase-js": "^2.53.0", "expo": "~53.0.20", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.2", "react-native-reanimated": "^4.0.1", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "typescript": "~5.8.3"}, "private": true}