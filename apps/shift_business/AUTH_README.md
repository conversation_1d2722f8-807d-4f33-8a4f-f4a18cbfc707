# Système d'Authentification Simplifié

Ce projet utilise un système d'authentification centralisé et simplifié basé sur Supabase.

## Structure

```
apps/shift_business/
├── AuthContext.tsx          # Contexte d'authentification principal
├── auth.ts                  # Point d'entrée pour les exports d'auth
├── useAuth.ts              # Hook personnalisé
├── services/
│   └── auth.tsx            # Service d'authentification
└── screens/
    ├── AuthScreen.tsx      # Écran de connexion/inscription
    └── HomeScreen.tsx      # Écran d'accueil avec déconnexion
```

## Utilisation

### 1. Configuration de base

L'authentification est configurée au niveau de l'App avec le `AuthProvider` :

```tsx
import { AuthProvider } from './AuthContext';

export default function App() {
  return (
    <AuthProvider>
      <Navigation />
    </AuthProvider>
  );
}
```

### 2. Utilisation du hook d'authentification

```tsx
import { useAuth } from './AuthContext';

function MonComposant() {
  const { user, loading, signIn, signOut } = useAuth();

  if (loading) return <Text>Chargement...</Text>;
  
  if (!user) {
    return <Text>Non connecté</Text>;
  }

  return (
    <View>
      <Text>Connecté en tant que: {user.email}</Text>
      <Button title="Se déconnecter" onPress={signOut} />
    </View>
  );
}
```

### 3. Connexion et inscription

```tsx
const { signIn, signUp } = useAuth();

// Connexion
await signIn('<EMAIL>', 'password');

// Inscription
await signUp('<EMAIL>', 'password');
```

## Fonctionnalités

- ✅ Connexion avec email/mot de passe
- ✅ Inscription avec email/mot de passe
- ✅ Déconnexion
- ✅ Gestion automatique de l'état d'authentification
- ✅ Persistance de la session
- ✅ Écran de chargement pendant l'initialisation
- ✅ Gestion d'erreurs avec alertes
- ✅ Interface utilisateur simple et intuitive

## API

### AuthContext

- `user`: Utilisateur actuel ou `null`
- `loading`: État de chargement
- `signIn(email, password)`: Fonction de connexion
- `signUp(email, password)`: Fonction d'inscription
- `signOut()`: Fonction de déconnexion

### AuthService

- `AuthService.signIn(email, password)`: Connexion
- `AuthService.signUp(email, password)`: Inscription
- `AuthService.signOut()`: Déconnexion
- `AuthService.getCurrentUser()`: Récupérer l'utilisateur actuel
- `AuthService.onAuthStateChange(callback)`: Écouter les changements d'état

## Configuration

Les variables d'environnement sont définies dans `constants/env.ts` :

```ts
export const SUPABASE_URL = 'votre-url-supabase';
export const SUPABASE_ANON_KEY = 'votre-clé-anonyme';
```

## Navigation

La navigation s'adapte automatiquement à l'état d'authentification :
- Si l'utilisateur est connecté → `HomeScreen`
- Si l'utilisateur n'est pas connecté → `AuthScreen`
- Pendant le chargement → `LoadingScreen`
