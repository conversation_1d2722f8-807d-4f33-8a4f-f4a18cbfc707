# auth/base.py
from fastapi import Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from supabase import create_client

# Init Supabase client
supabase = create_client("https://ojggxxcgrxtnxzbgvtyl.supabase.co", "sb_secret_JyGmpaohI9zlYCX5J9y5WA_V2PnjOWo")
security = HTTPBearer()

class BaseAuth:
    required_role = None  # à définir dans les sous-classes

    async def __call__(self, credentials: HTTPAuthorizationCredentials = Depends(security)):
        token = credentials.credentials
        user = self.verify_token_with_supabase(token)

        print("user : ", user)

        if self.required_role == "business":
            # try:
                query = (
                    supabase
                    .table("employers")
                    .select("id", count="exact")
                    .eq("user", user.id)
                    .limit(1)
                    .execute()
                )
                print("request : ", query, query.count)
            #     if query.count == 0:
            #         raise HTTPException(status_code=403, detail="Access denied")
            # except:
            #     raise HTTPException(status_code=403, detail="Access denied")
        elif self.required_role == "test":
            try:
                query = (
                    supabase
                    .table("worker")
                    .select("id", count="exact")
                    .eq("user_id", user_id)
                    .limit(1)
                )
            except:
                raise HTTPException(status_code=403, detail="Access denied")


        return user  # Tu peux retourner l'utilisateur au route handler

    def verify_token_with_supabase(self, token: str):
        try:
            response = supabase.auth.get_user(token)
            return response.user  # ou autre selon ta structure
        except Exception:
            return None
