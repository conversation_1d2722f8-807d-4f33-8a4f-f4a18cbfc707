# Shift Backend API

API backend Python avec FastAPI pour l'application Shift, avec authentification Supabase.

## 🚀 Installation et démarrage

### Prérequis
- Python 3.8+
- pip

### Installation

1. **C<PERSON>er un environnement virtuel**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # Sur Windows: venv\Scripts\activate
   ```

2. **Installer les dépendances**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configuration des variables d'environnement**
   
   Créez un fichier `.env` dans le dossier `backend/` :
   ```env
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_JWT_SECRET=your-jwt-secret-key
   ```

   **Comment obtenir ces valeurs :**
   - `SUPABASE_URL` : URL de votre projet Supabase (dans Settings > API)
   - `SUPABASE_JWT_SECRET` : JWT Secret de votre projet Supabase (dans Settings > API)

4. **Démarrer le serveur**
   ```bash
   python main.py
   ```
   
   Ou avec uvicorn directement :
   ```bash
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

L'API sera accessible sur `http://localhost:8000`

## 📚 Documentation API

Une fois le serveur démarré, la documentation interactive est disponible sur :
- **Swagger UI** : http://localhost:8000/docs
- **ReDoc** : http://localhost:8000/redoc

## 🔐 Authentification

L'API utilise l'authentification JWT de Supabase. Chaque requête protégée doit inclure le header :
```
Authorization: Bearer <supabase_jwt_token>
```

## 📋 Endpoints disponibles

### Authentification
- `GET /api/auth/verify` - Vérifier le token et récupérer les infos utilisateur

### Éléments (Items)
- `GET /api/items` - Récupérer tous les éléments de l'utilisateur
- `POST /api/items` - Créer un nouvel élément
- `PUT /api/items/{item_id}` - Mettre à jour un élément
- `DELETE /api/items/{item_id}` - Supprimer un élément

## 🔧 Configuration Supabase

### 1. Créer un projet Supabase
1. Allez sur [supabase.com](https://supabase.com)
2. Créez un nouveau projet
3. Notez l'URL et les clés API

### 2. Configuration de l'authentification
Dans le dashboard Supabase :
1. Allez dans **Authentication > Settings**
2. Configurez les providers d'authentification souhaités
3. Ajustez les paramètres de sécurité selon vos besoins

### 3. Récupérer les clés
Dans **Settings > API** :
- `URL` : URL de votre projet
- `anon public` : Clé publique pour l'app mobile
- `service_role` : Clé privée pour le backend (optionnel)
- `JWT Secret` : Secret pour vérifier les tokens JWT

## 🧪 Test de l'API

### Avec curl
```bash
# Vérifier l'authentification
curl -X GET "http://localhost:8000/api/auth/verify" \
  -H "Authorization: Bearer YOUR_SUPABASE_JWT_TOKEN"

# Créer un élément
curl -X POST "http://localhost:8000/api/items" \
  -H "Authorization: Bearer YOUR_SUPABASE_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title": "Mon élément", "description": "Description de test"}'
```

### Avec l'app mobile
L'app mobile Shift se connecte automatiquement à cette API une fois l'authentification configurée.

## 🔒 Sécurité

- Les tokens JWT sont vérifiés à chaque requête
- Chaque utilisateur ne peut accéder qu'à ses propres données
- CORS configuré pour les requêtes cross-origin
- Validation des données avec Pydantic

## 📝 Notes de développement

- La base de données est actuellement en mémoire (pour la démo)
- En production, utilisez une vraie base de données (PostgreSQL, MongoDB, etc.)
- Ajoutez des logs et monitoring appropriés
- Configurez HTTPS en production
- Limitez les origines CORS en production
