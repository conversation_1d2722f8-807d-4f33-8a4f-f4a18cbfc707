# routes/worker.py
from fastapi import APIRouter, Depends, HTTPException
from auth.worker import <PERSON><PERSON><PERSON>
from supabase import create_client
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

router = APIRouter()

# Init Supabase client
supabase = create_client("https://ojggxxcgrxtnxzbgvtyl.supabase.co", "sb_secret_JyGmpaohI9zlYCX5J9y5WA_V2PnjOWo")
security = HTTPBearer()

# Pydantic models
class ShiftApplication(BaseModel):
    shift_id: int

@router.post("/verify_access")
async def worker_access(user=Depends(WorkerAuth())):
    """Vérifier l'accès worker"""
    return {"message": "Accès worker autorisé", "auth": True}

@router.get("/get_worker_profile")
async def get_worker_profile(user=Depends(WorkerAuth())):
    """R<PERSON><PERSON><PERSON><PERSON> le profil complet du worker"""
    # try:
    print("user id : ", user["id"])
    worker_resp = (
        supabase
        .table("worker_infos")
        .select("*")
        .eq("id", user["id"])
        .limit(1)
        .execute()
    )

    print("worker_resp : ", worker_resp.data)

    if not worker_resp.data:
        raise HTTPException(status_code=404, detail="Profil worker non trouvé")

    return worker_resp.data[0]
    # except Exception as e:
    #     raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération du profil: {str(e)}")

@router.get("/get_available_shifts")
async def get_available_shifts(user=Depends(WorkerAuth())):
    """Récupérer les shifts disponibles pour le worker"""
    # try:
        # Récupérer le worker_id
    # worker_resp = (
    #     supabase
    #     .table("worker_infos")
    #     .select("id")
    #     .eq("worker", user["id"])
    #     .limit(1)
    #     .execute()
    # )

    # if not worker_resp.data:
    #     raise HTTPException(status_code=404, detail="Worker non trouvé")

    # worker_id = worker_resp.data[0]["id"]

    # Récupérer les shifts disponibles avec les informations du venue
    shifts_resp = (
        supabase
        .table("shifts")
        .select("""
            id, title, description, start_time, end_time, hourly_rate, currency,
            venue:venues(name, adresse)
        """)
        # .gte("start_time", datetime.now().isoformat())
        .order("start_time")
        .execute()
    )

    print("shifts_resp : ", shifts_resp.data)

    shifts = shifts_resp.data or []

    # # Vérifier le statut des candidatures pour chaque shift
    # for shift in shifts:
    #     app_resp = (
    #         supabase
    #         .table("shift_applications")
    #         .select("status")
    #         .eq("shift", shift["id"])
    #         .eq("worker", worker_id)
    #         .limit(1)
    #         .execute()
    #     )

    #     if app_resp.data:
    #         shift["status"] = "applied"
    #     else:
    #         shift["status"] = "available"

    #     # Ajouter le nom du venue
    #     if shift.get("venue"):
    #         shift["venue_name"] = shift["venue"]["name"]
    #         shift["venue_address"] = shift["venue"]["adresse"]

    return {"shifts": shifts}
    # except Exception as e:
    #     raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des shifts: {str(e)}")

@router.get("/check_application_status")
async def check_application_status(shift_id: int, user=Depends(WorkerAuth())):
    """Vérifier si le worker a déjà postulé pour un shift"""
    try:
        # Récupérer le worker_id
        worker_resp = (
            supabase
            .table("worker_infos")
            .select("id")
            .eq("user", user.id)
            .limit(1)
            .execute()
        )

        if not worker_resp.data:
            raise HTTPException(status_code=404, detail="Worker non trouvé")

        worker_id = worker_resp.data[0]["id"]

        # Vérifier l'existence d'une candidature
        app_resp = (
            supabase
            .table("shift_applications")
            .select("id, status")
            .eq("shift", shift_id)
            .eq("worker", worker_id)
            .limit(1)
            .execute()
        )

        has_applied = len(app_resp.data) > 0
        status = app_resp.data[0]["status"] if has_applied else None

        return {"has_applied": has_applied, "status": status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la vérification: {str(e)}")

@router.post("/apply_for_shift")
async def apply_for_shift(application: ShiftApplication, user=Depends(WorkerAuth())):
    """Postuler pour un shift"""
    try:
        # Récupérer le worker_id
        worker_resp = (
            supabase
            .table("worker_infos")
            .select("id")
            .eq("user", user.id)
            .limit(1)
            .execute()
        )

        if not worker_resp.data:
            raise HTTPException(status_code=404, detail="Worker non trouvé")

        worker_id = worker_resp.data[0]["id"]

        # Vérifier si une candidature existe déjà
        existing_app = (
            supabase
            .table("shift_applications")
            .select("id")
            .eq("shift", application.shift_id)
            .eq("worker", worker_id)
            .limit(1)
            .execute()
        )

        if existing_app.data:
            raise HTTPException(status_code=400, detail="Vous avez déjà postulé pour ce shift")

        # Créer la candidature
        app_resp = (
            supabase
            .table("shift_applications")
            .insert({
                "shift": application.shift_id,
                "worker": worker_id,
                "status": "pending",
                "applied_at": datetime.now().isoformat()
            })
            .execute()
        )

        return {"message": "Candidature envoyée avec succès", "application_id": app_resp.data[0]["id"]}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la candidature: {str(e)}")

@router.get("/get_worker_stats")
async def get_worker_stats(user=Depends(WorkerAuth())):
    """Récupérer les statistiques du worker"""
    try:
        # Récupérer le worker_id
        worker_resp = (
            supabase
            .table("worker_infos")
            .select("id")
            .eq("user", user.id)
            .limit(1)
            .execute()
        )

        if not worker_resp.data:
            raise HTTPException(status_code=404, detail="Worker non trouvé")

        worker_id = worker_resp.data[0]["id"]

        # Statistiques des shifts terminés
        completed_shifts_resp = (
            supabase
            .table("shift_applications")
            .select("shift:shifts(hourly_rate, currency, start_time, end_time)")
            .eq("worker", worker_id)
            .eq("status", "completed")
            .execute()
        )

        completed_shifts = completed_shifts_resp.data or []

        # Calculer les statistiques
        total_shifts_worked = len(completed_shifts)
        total_hours_worked = 0
        total_earnings = 0
        currency = "EUR"  # Par défaut

        for shift_app in completed_shifts:
            shift = shift_app.get("shift", {})
            if shift:
                start_time = datetime.fromisoformat(shift["start_time"].replace("Z", "+00:00"))
                end_time = datetime.fromisoformat(shift["end_time"].replace("Z", "+00:00"))
                hours = (end_time - start_time).total_seconds() / 3600
                total_hours_worked += hours
                total_earnings += hours * float(shift["hourly_rate"])
                currency = shift["currency"]

        # Statistiques du mois en cours
        current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        monthly_shifts_resp = (
            supabase
            .table("shift_applications")
            .select("shift:shifts(hourly_rate, currency, start_time, end_time)")
            .eq("worker", worker_id)
            .eq("status", "completed")
            .gte("shift.start_time", current_month.isoformat())
            .execute()
        )

        monthly_shifts = monthly_shifts_resp.data or []
        shifts_this_month = len(monthly_shifts)
        hours_this_month = 0
        earnings_this_month = 0

        for shift_app in monthly_shifts:
            shift = shift_app.get("shift", {})
            if shift:
                start_time = datetime.fromisoformat(shift["start_time"].replace("Z", "+00:00"))
                end_time = datetime.fromisoformat(shift["end_time"].replace("Z", "+00:00"))
                hours = (end_time - start_time).total_seconds() / 3600
                hours_this_month += hours
                earnings_this_month += hours * float(shift["hourly_rate"])

        # Statistiques des candidatures
        all_applications_resp = (
            supabase
            .table("shift_applications")
            .select("status")
            .eq("worker", worker_id)
            .execute()
        )

        applications = all_applications_resp.data or []
        pending_applications = len([app for app in applications if app["status"] == "pending"])
        accepted_applications = len([app for app in applications if app["status"] in ["accepted", "confirmed"]])

        # Note moyenne (simulée pour l'instant)
        average_rating = 4.2  # À implémenter avec une vraie table de ratings

        return {
            "total_shifts_worked": total_shifts_worked,
            "total_hours_worked": round(total_hours_worked, 1),
            "total_earnings": round(total_earnings, 2),
            "currency": currency,
            "average_rating": average_rating,
            "shifts_this_month": shifts_this_month,
            "hours_this_month": round(hours_this_month, 1),
            "earnings_this_month": round(earnings_this_month, 2),
            "pending_applications": pending_applications,
            "accepted_applications": accepted_applications,
            "completed_shifts": total_shifts_worked
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des statistiques: {str(e)}")

@router.get("/get_recent_shifts")
async def get_recent_shifts(user=Depends(WorkerAuth())):
    """Récupérer les shifts récents du worker"""
    try:
        # Récupérer le worker_id
        worker_resp = (
            supabase
            .table("worker_infos")
            .select("id")
            .eq("user", user.id)
            .limit(1)
            .execute()
        )

        if not worker_resp.data:
            raise HTTPException(status_code=404, detail="Worker non trouvé")

        worker_id = worker_resp.data[0]["id"]

        # Récupérer les shifts récents avec les informations du venue
        recent_shifts_resp = (
            supabase
            .table("shift_applications")
            .select("""
                status, applied_at,
                shift:shifts(id, title, start_time, end_time, hourly_rate, currency,
                venue:venues(name))
            """)
            .eq("worker", worker_id)
            .order("applied_at", desc=True)
            .limit(10)
            .execute()
        )

        applications = recent_shifts_resp.data or []
        shifts = []

        for app in applications:
            shift_data = app.get("shift", {})
            if shift_data:
                # Calculer les heures travaillées et les gains
                start_time = datetime.fromisoformat(shift_data["start_time"].replace("Z", "+00:00"))
                end_time = datetime.fromisoformat(shift_data["end_time"].replace("Z", "+00:00"))
                hours_worked = (end_time - start_time).total_seconds() / 3600
                earnings = hours_worked * float(shift_data["hourly_rate"])

                shifts.append({
                    "id": shift_data["id"],
                    "title": shift_data["title"],
                    "venue_name": shift_data.get("venue", {}).get("name", ""),
                    "date": shift_data["start_time"],
                    "hours_worked": round(hours_worked, 1),
                    "earnings": round(earnings, 2),
                    "status": app["status"]
                })

        return {"shifts": shifts}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des shifts récents: {str(e)}")
