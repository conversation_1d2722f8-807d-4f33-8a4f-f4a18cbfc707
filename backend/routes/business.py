# routes/employer.py
from fastapi import APIRouter, Depends
from auth.business import BusinessAuth, VerifyAuth
from supabase import create_client
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials

router = APIRouter()

# Init Supabase client
supabase = create_client("https://ojggxxcgrxtnxzbgvtyl.supabase.co", "sb_secret_JyGmpaohI9zlYCX5J9y5WA_V2PnjOWo")
security = HTTPBearer()

@router.get("/verify_access")
async def employer_access(user=Depends(BusinessAuth())):
    return {"message": "Bienvenue pro", "auth": True}

@router.get("/get_companies")
async def get_companies(user=Depends(BusinessAuth())):

    employer_resp = (
        supabase
        .table("employers")
        .select("id")
        .eq("user", user.id)
        .limit(1)
        .execute()
    )

    employer_data = employer_resp.data

    if not employer_data:
        raise HTTPException(status_code=404, detail="Employer not found for user")

    employer_id = employer_data[0]["id"]

    # 2. Récupération des entreprises liées à cet employer
    companies_resp = (
        supabase
        .table("companies_membership")
        .select("company(id, name)")
        .eq("employer", employer_id)
        .execute()
    )

    raw_companies = companies_resp.data or []
    companies = [item["company"] for item in raw_companies if "company" in item]

    return {
        "companies": companies
    }


@router.get("/get_lieux")
async def get_lieux(company_id: int, user=Depends(BusinessAuth())):
    
    lieux_resp = (
        supabase
        .table("venues")
        .select("id, name, logo")
        .eq("company", company_id)
        .execute()
    )
    lieux = lieux_resp.data or []
    return {"lieux": lieux}


@router.get("/get_lieux_info")
async def get_lieux_info(venue_id: int, user=Depends(BusinessAuth())):
    
    lieu_resp = (
        supabase
        .table("venues")
        .select("id, name, logo, type, cuisine, email, phone_number, adresse, postal_code, town, country, created_at")
        .eq("id", venue_id)
        .execute()
    )
    lieu = lieu_resp.data[0] or {}

    print("lieu : ", lieu)
    return lieu

@router.get("/get_shifts_by_venue")
async def get_shifts_by_venue(venue_id: int, user=Depends(BusinessAuth())):
    
    shifts_resp = (
        supabase
        .table("shifts")
        .select("id, title, description, start_time, end_time, hourly_rate, currency")
        .eq("venue", venue_id)
        .execute()
    )
    shifts = shifts_resp.data or []
    return shifts

@router.get("/get_venue_workers")
async def get_venue_workers(venue_id: int, user=Depends(BusinessAuth())):
    # Étape 1 : Récupérer les IDs des workers liés à ce venue
    workers_links_resp = (
        supabase
        .table("venue_pool_workers")
        .select("worker")
        .eq("venue", venue_id)
        .execute()
    )

    worker_ids = [row["worker"] for row in (workers_links_resp.data or [])]
    print("worker_ids : ", worker_ids)
    if not worker_ids:
        return []

    # Étape 2 : Récupérer les infos des workers
    workers_resp = (
        supabase
        .table("worker_infos")
        .select("id, first_name, last_name, experience_level, profile_picture")
        .in_("id", worker_ids)
        .execute()
    )

    return workers_resp.data or []


@router.get("/get_worker_info")
async def get_worker_info(worker_id: int, user=Depends(BusinessAuth())):
    res = (
        supabase
        .table("worker_infos")
        .select("*")
        .eq("id", worker_id)
        .limit(1)
        .execute()
    )
    if not res.data:
        raise HTTPException(status_code=404, detail="Worker not found")
    return res.data[0]

@router.get("/get_shift_applications")
async def get_shift_applications(shift_id: int, user=Depends(BusinessAuth())):
    res = (
        supabase
        .table("shift_applications")
        .select("id, worker(id, first_name, last_name), status")
        .eq("shift", shift_id)
        .execute()
    )
    return res.data or []

@router.get("/get_companies")
async def employer_dashboard(user=Depends(BusinessAuth())):
    
    # # try:
    employer_query = (
        supabase
        .table("employers")
        .select("id")
        .eq("user", user.id)
        .limit(1)
        .execute()
    )

    print("employers id : ", employer_query)

    # query = (
    #     supabase
    #     .table("companies_membership")
    #     .select("companies:company(*)")  # jointure vers la table companies
    #     .eq("employer", employer_query)        # ou user.id selon ton objet
    # )

    # response = query.execute()
    # print("response : ", response)
    # companies = [item["name"] for item in response.data]
    # print('query : ', companies)
    # # except:
    # #     raise HTTPException(status_code=403, detail="Access denied")
    
    return {"message": "entreprises", "user": user}
