from fastapi import FastAPI
from routes import business, worker

app = FastAPI()

from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Ou liste précise des origines
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(worker.router, prefix="/worker")
app.include_router(business.router, prefix="/business")

# Optionnel : point de départ simple
@app.get("/")
async def root():
    return {"message": "API is running"}